import streamlit as st
import table

st.set_page_config(
    page_title="Dashboard",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Add custom styling
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #2b6cb0;
        margin-bottom: 1rem;
    }

    .sidebar-header {
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 1rem;
    }

    .stTabs [data-baseweb="tab-list"] {
        gap: 2px;
    }

    .stTabs [data-baseweb="tab"] {
        height: 50px;
        white-space: pre-wrap;
        background-color: #f1f5f9;
        border-radius: 4px 4px 0 0;
        gap: 1px;
        padding-top: 10px;
        padding-bottom: 10px;
    }

    .stTabs [aria-selected="true"] {
        background-color: #2b6cb0;
        color: white;
    }
</style>
""", unsafe_allow_html=True)

# Sidebar navigation
st.sidebar.markdown('<div class="sidebar-header">Navigation</div>', unsafe_allow_html=True)

# Create navigation options
pages = {
    "Accueil": "home",
    "Données Oracle": "oracle_data"
}

# Create radio buttons for navigation
selected_page = st.sidebar.radio("", list(pages.keys()))

# Display the selected page
if selected_page == "Accueil":
    st.markdown('<div class="main-header">Bienvenue sur le Dashboard</div>', unsafe_allow_html=True)
    st.write("Sélectionnez une page dans le menu de navigation à gauche.")

    # Display some basic information
    st.info("Ce dashboard vous permet de visualiser et de gérer les données de l'application.")

    # Add some quick links
    st.markdown("### Accès rapide")
    col1, _ = st.columns([1, 2])

    with col1:
        if st.button("📊 Données Oracle", use_container_width=True):
            selected_page = "Données Oracle"
            st.rerun()

elif selected_page == "Données Oracle":
    table.app()

# Add footer
st.sidebar.markdown("---")
st.sidebar.info("© 2023 Dashboard")
