import streamlit as st
import pdfplumber
import re
import pandas as pd
import os

def app():
    # Path to your PDF file


    pdf_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "Bulletin de Sécurité- SHRS-SEC-2023-09  - v7.0 1.pdf")


    # Function to extract all necessary information under section "2."
    def extract_vulnerabilities_data(pdf_path):
        raw_data = []
        section_start = False
        current_vuln = None
        processing_first_subheader = False
        processing_second_subheader = False

        with pdfplumber.open(pdf_path) as pdf:
            for page in pdf.pages:
                text = page.extract_text() 
                lines = text.splitlines()

                for i, line in enumerate(lines):
                    if line.strip().startswith("2. Corrections cumulatives de sécurité"):
                        section_start = True

                    if section_start and re.match(r"^\d\.\d+.*", line):
                        match = re.search(r"(SEC-\d{4}-\d{4})\s*:\s*(.+)", line)
                        if match:
                            vuln_id = match.group(1)
                            description = re.sub(r"\s+\d+$", "", match.group(2).strip())
                            current_vuln = {
                                "Vulnérabilité ID": vuln_id,
                                "Description de la vulnérabilité": description,
                                "Version affectée": "",
                                "Indice de gravité": "",
                                "Installation": "",
                                "Patch": "",
                            }
                            raw_data.append(current_vuln)
                            processing_first_subheader = False
                            processing_second_subheader = False

                    if current_vuln and re.match(r"^\d\.\d+\.\d+\..*", line):
                        if not processing_first_subheader:
                            processing_first_subheader = True
                            version_affectee_list = []
                            for next_line in lines[i + 1:]:
                                if re.match(r"^\d\.\d+\.\d+\..*", next_line):
                                    break
                                bullet_match = re.search(r"•\s*(.+)", next_line)
                                if bullet_match:
                                    version_affectee_list.append(bullet_match.group(1).strip())
                                gravity_match = re.search(r"gravité.*?\"([^\"]+)\"", next_line, re.IGNORECASE)
                                if gravity_match and current_vuln["Indice de gravité"] == "":
                                    current_vuln["Indice de gravité"] = gravity_match.group(1).strip()

                            current_vuln["Version affectée"] = " | ".join(version_affectee_list)

                        elif not processing_second_subheader:
                            processing_second_subheader = True
                            patch_numbers = set()
                            for next_line in lines[i + 1:]:
                                if re.match(r"^\d\.\d+\.\d+\..*", next_line):
                                    break
                                if "facultative" in next_line.lower():
                                    current_vuln["Installation"] = "facultative"
                                elif "obligatoire" in next_line.lower():
                                    current_vuln["Installation"] = "obligatoire"
                                patch_matches = re.findall(r"#\d+", next_line)
                                patch_numbers.update(patch_matches)

                            if patch_numbers:
                                current_vuln["Patch"] = " | ".join(sorted(patch_numbers))

                if section_start and line.strip().startswith("3. "):
                    section_start = False
                    break

        # Remove duplicates by merging data with the same Vulnérabilité ID
        unique_data = {}
        for entry in raw_data:
            vuln_id = entry["Vulnérabilité ID"]
            if vuln_id not in unique_data:
                unique_data[vuln_id] = entry
            else:
                if not unique_data[vuln_id]["Version affectée"] and entry["Version affectée"]:
                    unique_data[vuln_id]["Version affectée"] = entry["Version affectée"]
                if not unique_data[vuln_id]["Indice de gravité"] and entry["Indice de gravité"]:
                    unique_data[vuln_id]["Indice de gravité"] = entry["Indice de gravité"]
                if not unique_data[vuln_id]["Installation"] and entry["Installation"]:
                    unique_data[vuln_id]["Installation"] = entry["Installation"]
                if not unique_data[vuln_id]["Patch"] and entry["Patch"]:
                    unique_data[vuln_id]["Patch"] = entry["Patch"]

        return pd.DataFrame(list(unique_data.values()))  # Return a DataFrame

    # Extract vulnerabilities and display
    try:
        vulnerabilities_df = extract_vulnerabilities_data(pdf_path)

        if not vulnerabilities_df.empty:
            # Define two separate lists for HRa Tools and HRConfiguration Tools
            hra_tools_versions = [
                "HRa Tools 7.40.060 édition 04 (ML4)",  
                "HRa Tools 7.40.060 édition 02 (ML2)",
                "HRa Tools 7.40.060 édition 01 (ML1)",
                "HRa Tools 7.40.050 édition 05 (ML5)",  
                "HRa Tools 7.40.050 édition 04 (ML4)",  
                "HRa Tools 7.40.050 édition 03 (ML3)",  
                "HRa Tools 7.40.050 édition 02 (ML2)", 
                "HRa Tools 7.40.050 édition 01 (ML1)",  
                "HRa Tools 7.40.040 édition 05 (ML5)",  
                "HRa Tools 7.40.040 édition 04 (ML4)",  
                "HRa Tools 7.40.040 édition 03 (ML3)",  
                "HRa Tools 7.40.040 édition 02 (ML2)",  
                "HRa Tools 7.40.040 édition 01 (ML1)",  
            ]

            hrconfig_tools_versions = [
                "HRConfiguration Tools V2.7.14 (ML1)",  
                "HRConfiguration Tools V2.7.9 (ML1)",  
                "HRConfiguration Tools V2.7.8 (ML1)",  
                "HRConfiguration Tools V2.7.6 (ML1)",  
                "HRConfiguration Tools V2.7.5 (ML1)"  
            ]

            def expand_version(selected):
                """Expands version selection, considering only the relevant category."""
                valid_versions = [selected]  # Always include the selected version itself

                # Determine which category the selected version belongs to
                if any(selected.startswith(prefix) for prefix in ["HRa Tools"]):
                    relevant_versions = hra_tools_versions
                elif any(selected.startswith(prefix) for prefix in ["HRConfiguration Tools"]):
                    relevant_versions = hrconfig_tools_versions
                else:
                    return valid_versions  # If unknown, return only selected

                # Expand "et inférieures" logic only within the relevant category
                if "et inférieures" in selected:
                    selected_version = selected.replace(" et inférieures", "").strip()
                    valid_versions.append(selected_version)  # Add the base version

                    if selected_version in relevant_versions:
                        index = relevant_versions.index(selected_version)
                        lower_versions = relevant_versions[index+1:]  # Get all lower versions
                        
                        valid_versions.extend(lower_versions)  # Include lower versions
                        
                        # Check if any lower versions also have 'et inférieures' and add them
                        for v in lower_versions:
                            lower_with_suffix = f"{v} et inférieures"
                            if lower_with_suffix in vulnerabilities_df["Version affectée"].unique():
                                valid_versions.append(lower_with_suffix)  # Add if found in DataFrame

                return valid_versions

            # Get all unique versions but split multiple values
            unique_versions = set()
            for versions in vulnerabilities_df["Version affectée"].dropna().unique():
                unique_versions.update(versions.split(" | "))  # Split and add individually

            col1, col2 = st.columns(2)
            with col1:
                selected_versions = st.multiselect("Version affectée", options=sorted(unique_versions), placeholder="Sélectionner une option")
            with col2:
                selected_gravities = st.multiselect("Indice de gravité", options=vulnerabilities_df["Indice de gravité"].unique(), placeholder="Sélectionner une option")

            expanded_versions = set()
            for version in selected_versions:
                expanded_versions.update(expand_version(version))

            def match_versions(row_versions, selected_versions):
                row_versions_list = row_versions.split(" | ")  # Convert to list
                return any(version.strip() in selected_versions for version in row_versions_list)

            if expanded_versions:
                filtered_data = vulnerabilities_df[vulnerabilities_df["Version affectée"].apply(lambda x: match_versions(x, expanded_versions))]
            else:
                filtered_data = vulnerabilities_df

            col3, col4 = st.columns(2)
            with col3:
                selected_installation = st.multiselect("Installation", options=vulnerabilities_df["Installation"].unique(), placeholder="Sélectionner une option")
            with col4:
                selected_patches = st.multiselect("Patch", options=vulnerabilities_df["Patch"].unique(), placeholder="Sélectionner une option")


            if selected_gravities:
                filtered_data = filtered_data[filtered_data["Indice de gravité"].isin(selected_gravities)]
            if selected_installation:
                filtered_data = filtered_data[filtered_data["Installation"].isin(selected_installation)]
            if selected_patches:
                filtered_data = filtered_data[filtered_data["Patch"].isin(selected_patches)]

            # Convert the extracted data into a Pandas DataFrame
            df = pd.DataFrame(filtered_data)

            # Function to apply conditional formatting to the DataFrame
            def apply_priority_colors(df):
                def priority_color(row):
                    if row['Indice de gravité'] == 'fort':  # Matching 'Indice de gravité' column
                        return ['background-color: #FF0000; color: white'] * len(row)  # Red for P1
                    elif row['Indice de gravité'] == 'moyen':
                        return ['background-color: #ED7D31; color: black'] * len(row)  # Orange for P2
                    elif row['Indice de gravité'] == 'faible':
                        return ['background-color: #FFC000; color: black'] * len(row)  # Yellow for P3
                    else:
                        return [''] * len(row)  # Default (no styling)

                return df.style.apply(priority_color, axis=1)

            # Apply priority color formatting
            styled_table = apply_priority_colors(df)

            # Use Pandas Styler to center headers and hide index
            styled_table = (
                styled_table.hide(axis='index')  # Hide the index
                .set_table_styles(
                    [{'selector': 'th', 'props': [('text-align', 'center')]}]  # Center headers
                )
            )

            # Convert to HTML with additional spacing
            table_html = styled_table.to_html()
            centered_table_html = f"""
            <div style="margin-top: 20px;">
                {table_html}
            """

            # Display in Streamlit
            st.write(centered_table_html, unsafe_allow_html=True)
            with open(pdf_path, "rb") as pdf_file:
                pdf_data = pdf_file.read()
            st.download_button(
                label="Télécharger le PDF",
                data=pdf_data,
                file_name="Bulletin_de_Sécurité.pdf",
                mime="application/pdf"
            )


        else:
            st.warning("No vulnerabilities found in section 2.")
    except Exception as e:
        st.error(f"An error occurred while processing the PDF: {e}")


if __name__ == "__main__":
    app()
