import streamlit as st
import pandas as pd
import plotly.express as px
from datetime import datetime, timedelta
import os
import re  # for cleaning string dates
import uuid  # for unique chart keys
import io  # for BytesIO
from database import get_connection, update_row, insert_row, get_table_structure, check_value_exists, get_primary_key_columns, get_exact_column_names

# Define normalize_col_name function from testt.py
def normalize_col_name(name):
    return re.sub(r'\s+', '', name.lower())

# Define transform_statut function from testt.py
def transform_statut(value):
    normalized = re.sub(r'\s+', '', value).lower()
    if any(keyword in normalized for keyword in ['clos', 'corrigée', 'corrigé', 'clôs']):
        return 'Corrigée'
    elif 'encours' in normalized:
        return 'En cours'
    else:
        return value

# --- Table display function (HTML) ---
def display_table_only(df, show_year=True):
    required = ["priorité", "vulnérabilité", "statut"]
    if show_year:
        required.append("année")
    for col in required:
        if col not in df.columns:
            st.warning("Required column not found: " + col)
            return

    show_client = "client" in df.columns
    priority_order = ["P1", "P2", "P3"]
    df["priorité"] = pd.Categorical(df["priorité"], categories=priority_order, ordered=True)

    def priority_style(p):
        if p == 'P1':
            return 'background-color: #FF0000; color: white;'
        elif p == 'P2':
            return 'background-color: #ED7D31; color: black;'
        elif p == 'P3':
            return 'background-color: #FFC000; color: black;'
        return ''

    # If client data is available, pivot the table
    if show_client:
        # Check if version column exists for two-level pivot
        has_version = "version" in df.columns

        # STEP 0: NORMALIZE VULNERABILITY NAMES - Handle case differences and variations
        # Create a copy to avoid modifying original data
        df_normalized = df.copy()

        # Normalize vulnerability names: lowercase, strip whitespace, handle common variations
        def normalize_vulnerability(vuln):
            if pd.isna(vuln):
                return vuln
            normalized = str(vuln).lower().strip()
            # Add more normalization rules as needed
            normalized = normalized.replace('  ', ' ')  # Replace double spaces with single space
            return normalized

        df_normalized['vulnérabilité_normalized'] = df_normalized['vulnérabilité'].apply(normalize_vulnerability)
        
        # Create pivot table: rows = Priorité + Vulnérabilité, columns = Client + Version (if available)
        pivot_cols = ["priorité", "vulnérabilité_normalized"]
        if show_year:
            pivot_cols.insert(1, "année")

        # Add fix_type column to pivot if it exists in the data
        if 'fix_type' in df_normalized.columns:
            pivot_cols.append('fix_type')

        if has_version:
            # STEP 1: Create four-level column index pivot table
            # First, create a combined client-restitution-version identifier for pivot
            df_normalized['client_restitution_version'] = (df_normalized['client'].astype(str) + '|' +
                                                          df_normalized['restitution_date'].fillna('No Date').astype(str) + '|' +
                                                          df_normalized['version'].fillna('').astype(str))

            # Create pivot table with client-restitution-version combinations
            pivot_df = df_normalized.pivot_table(
                index=pivot_cols,
                columns="client_restitution_version",
                values="statut",
                aggfunc=lambda x: ' / '.join(x.dropna().unique()) if len(x.dropna().unique()) > 1 else x.iloc[0] if len(x.dropna()) > 0 else "N/A",
                fill_value="N/A"
            ).reset_index()

            # STEP 2: Convert to four-level MultiIndex columns

            # Create mapping for three-level columns
            multi_index_tuples = []
            for col in pivot_df.columns:
                if col in pivot_cols:
                    multi_index_tuples.append(('', '', col))  # Empty first two levels for index columns
                elif '|' in str(col):
                    parts = str(col).split('|')
                    if len(parts) >= 3:
                        client, restitution_date, version = parts[0], parts[1], parts[2]
                        version_header = f"Version: {version}" if version else "Version: No Version"
                        date_header = f"Date restitution: {restitution_date}" if restitution_date != "No Date" else "Date restitution: No Date"
                        multi_index_tuples.append((client, version_header, date_header))
                    else:
                        multi_index_tuples.append(('', '', col))
                else:
                    multi_index_tuples.append(('', '', col))

            # Create MultiIndex columns
            pivot_df.columns = pd.MultiIndex.from_tuples(multi_index_tuples, names=['Client', 'Version', 'Date'])

        else:
            # STEP 1: MERGE FIRST - Create a pivot table that merges all client data
            # for each Priorité + Vulnérabilité combination to preserve all data (original logic)
            pivot_df = df_normalized.pivot_table(
                index=pivot_cols,
                columns="client",
                values="statut",
                aggfunc=lambda x: ' / '.join(x.dropna().unique()) if len(x.dropna().unique()) > 1 else x.iloc[0] if len(x.dropna()) > 0 else "N/A",  # Merge multiple statuses with separator
                fill_value="N/A"
            ).reset_index()

        # STEP 3: Get the original vulnerability name (with proper capitalization) for display
        # Map normalized names back to original names for better display
        vuln_mapping = df_normalized.groupby('vulnérabilité_normalized')['vulnérabilité'].first().to_dict()

        # Add recommandation data by mapping from original data
        if 'recommandation' in df_normalized.columns:
            remediation_mapping = df_normalized.groupby('vulnérabilité_normalized')['recommandation'].first().to_dict()

        if has_version:
            # For MultiIndex columns, update the vulnerability column
            pivot_df[('', '', 'vulnérabilité')] = pivot_df[('', '', 'vulnérabilité_normalized')].map(vuln_mapping)
            # Add recommandation column
            if 'recommandation' in df_normalized.columns:
                pivot_df[('', '', 'recommandation')] = pivot_df[('', '', 'vulnérabilité_normalized')].map(remediation_mapping)
            pivot_df = pivot_df.drop(('', '', 'vulnérabilité_normalized'), axis=1)
        else:
            pivot_df['vulnérabilité'] = pivot_df['vulnérabilité_normalized'].map(vuln_mapping)
            # Add recommandation column
            if 'recommandation' in df_normalized.columns:
                pivot_df['recommandation'] = pivot_df['vulnérabilité_normalized'].map(remediation_mapping)
            pivot_df = pivot_df.drop('vulnérabilité_normalized', axis=1)

        # STEP 4: THEN DEDUPLICATE - Remove any remaining duplicate rows (if any exist after merging)
        if has_version:
            final_pivot_cols = [('', '', "priorité"), ('', '', "vulnérabilité"), ('', '', "recommandation")]
            if show_year:
                final_pivot_cols.insert(1, ('', '', "année"))
            pivot_df = pivot_df.drop_duplicates(subset=final_pivot_cols).reset_index(drop=True)
            # Sort by priority
            pivot_df = pivot_df.sort_values(by=('', '', "priorité"))
        else:
            final_pivot_cols = ["priorité", "vulnérabilité", "recommandation"]
            if show_year:
                final_pivot_cols.insert(1, "année")
            # Reorder columns
            client_columns = [col for col in pivot_df.columns if col not in final_pivot_cols]
            pivot_df = pivot_df[final_pivot_cols + client_columns]
            pivot_df = pivot_df.drop_duplicates(subset=final_pivot_cols).reset_index(drop=True)
            # Sort by priority
            pivot_df = pivot_df.sort_values(by="priorité")

        # Generate HTML table
        html = '<table style="width:100%; border-collapse: collapse;" border="1">'

        if has_version:
            # Generate three-level header for MultiIndex columns
            html += '<thead>'

            # First header row (Client level)
            html += '<tr style="text-align: center; background-color: #f2f2f2;">'
            html += '<th rowspan="3">Priorité</th>'
            if show_year:
                html += '<th rowspan="3">Année</th>'
            html += '<th rowspan="3">Vulnérabilité</th>'
            html += '<th rowspan="3">Remédiation</th>'

            # Group columns by client for header spanning
            client_groups = {}
            for col in pivot_df.columns:
                if isinstance(col, tuple) and col[0] != '':  # Skip index columns
                    client, version_header, date_header = col
                    if client not in client_groups:
                        client_groups[client] = {'versions': {}}
                    if version_header not in client_groups[client]['versions']:
                        client_groups[client]['versions'][version_header] = date_header

            # Add client headers with appropriate colspan
            for client, data in client_groups.items():
                html += f'<th colspan="{len(data["versions"])}">{client}</th>'
            html += '</tr>'

            # Second header row (Version level)
            html += '<tr style="text-align: center; background-color: #f2f2f2;">'
            for client, data in client_groups.items():
                for version_header in data['versions'].keys():
                    html += f'<th>{version_header}</th>'
            html += '</tr>'

            # Third header row (Date level)
            html += '<tr style="text-align: center; background-color: #f2f2f2;">'
            for client, data in client_groups.items():
                for version_header, date_header in data['versions'].items():
                    html += f'<th>{date_header}</th>'
            html += '</tr>'
            html += '</thead><tbody>'
            
            # Calculate total columns for header spanning
            total_columns = 4  # Priorité, Vulnérabilité, Remédiation, and one more base column
            if show_year:
                total_columns += 1  # Add année column
            # Add client-version combinations
            for client, data in client_groups.items():
                total_columns += len(data['versions'])

        
            
            # Filter for applicative vulnerabilities
            if ('', '', 'fix_type') in pivot_df.columns:
                # Filter for anything starting with 'A' or 'a' (Applicatif, Applicative, etc.)
                applicative_df = pivot_df[pivot_df[('', '', 'fix_type')].str.strip().str.lower().str.startswith('a')]
            else:
                applicative_df = pd.DataFrame()  # Empty if no fix_type column

            # Only show Failles Applicatives section if there are applicative vulnerabilities
            if not applicative_df.empty:
                # Add a header row for "Failles applicatives"
                html += f'<tr style="background-color: #e6e6e6;"><td colspan="{total_columns}" style="text-align:left; font-weight:bold; padding:10px;">Failles Applicatives</td></tr>'

                for priority, pgroup in applicative_df.groupby(('', '', "priorité"), sort=False):
                    total_p = len(pgroup)
                    row_style = priority_style(priority)
                    first_priority_cell = True
                    if show_year:
                        for year, ygroup in pgroup.groupby(('', '', "année"), sort=False):
                            total_y = len(ygroup)
                            first_year_cell = True
                            for _, row in ygroup.iterrows():
                                html += f'<tr style="{row_style}">'
                                if first_priority_cell:
                                    html += f'<td rowspan="{total_p}" style="text-align:center;">{priority}</td>'
                                    first_priority_cell = False
                                if first_year_cell:
                                    html += f'<td rowspan="{total_y}" style="text-align:center;">{year}</td>'
                                    first_year_cell = False
                                html += f'<td>{row[("", "", "vulnérabilité")]}</td>'
                                # Add remédiation column
                                remediation_value = row.get(("", "", "recommandation"), "")
                                html += f'<td>{remediation_value}</td>'
                                # Add data cells for each client-version combination
                                for client, data in client_groups.items():
                                    for version_header, date_header in data['versions'].items():
                                        cell_value = row.get((client, version_header, date_header), "N/A")
                                        if pd.isna(cell_value) or cell_value == "":
                                            cell_value = "N/A"
                                        html += f'<td style="text-align:center;">{cell_value}</td>'
                                html += '</tr>'
                    else:
                        for _, row in pgroup.iterrows():
                            html += f'<tr style="{row_style}">'
                            if first_priority_cell:
                                html += f'<td rowspan="{total_p}" style="text-align:center;">{priority}</td>'
                                first_priority_cell = False
                            html += f'<td>{row[("", "", "vulnérabilité")]}</td>'
                            # Add remédiation column
                            remediation_value = row.get(("", "", "recommandation"), "")
                            html += f'<td>{remediation_value}</td>'
                            # Add data cells for each client-version combination
                            for client, data in client_groups.items():
                                for version_header, date_header in data['versions'].items():
                                    cell_value = row.get((client, version_header, date_header), "N/A")
                                    if pd.isna(cell_value) or cell_value == "":
                                        cell_value = "N/A"
                                    html += f'<td style="text-align:center;">{cell_value}</td>'
                            html += '</tr>'
            
            # After displaying all applicative vulnerabilities, add infrastructure vulnerabilities
            
            # Add a header row for "Failles infrastructure"
            html += f'<tr style="background-color: #e6e6e6;"><td colspan="{total_columns}" style="text-align:left; font-weight:bold; padding:10px;">Failles Infrastructure</td></tr>'
            
            # Filter for infrastructure vulnerabilities
            if ('', '', 'fix_type') in pivot_df.columns:
                # Filter for anything starting with 'I' or 'i' (Infrastructure, Infra, etc.)
                infrastructure_df = pivot_df[pivot_df[('', '', 'fix_type')].str.strip().str.lower().str.startswith('i')]
            else:
                infrastructure_df = pd.DataFrame()
            
            # Only proceed if there are infrastructure vulnerabilities
            if not infrastructure_df.empty:
                for priority, pgroup in infrastructure_df.groupby(('', '', "priorité"), sort=False):
                    total_p = len(pgroup)
                    row_style = priority_style(priority)
                    first_priority_cell = True
                    if show_year:
                        for year, ygroup in pgroup.groupby(('', '', "année"), sort=False):
                            total_y = len(ygroup)
                            first_year_cell = True
                            for _, row in ygroup.iterrows():
                                html += f'<tr style="{row_style}">'
                                if first_priority_cell:
                                    html += f'<td rowspan="{total_p}" style="text-align:center;">{priority}</td>'
                                    first_priority_cell = False
                                if first_year_cell:
                                    html += f'<td rowspan="{total_y}" style="text-align:center;">{year}</td>'
                                    first_year_cell = False
                                html += f'<td>{row[("", "", "vulnérabilité")]}</td>'
                                # Add remédiation column
                                remediation_value = row.get(("", "", "recommandation"), "")
                                html += f'<td>{remediation_value}</td>'
                                # Add data cells for each client-version combination
                                for client, data in client_groups.items():
                                    for version_header, date_header in data['versions'].items():
                                        cell_value = row.get((client, version_header, date_header), "N/A")
                                        if pd.isna(cell_value) or cell_value == "":
                                            cell_value = "N/A"
                                        html += f'<td style="text-align:center;">{cell_value}</td>'
                                html += '</tr>'
                    else:
                        for _, row in pgroup.iterrows():
                            html += f'<tr style="{row_style}">'
                            if first_priority_cell:
                                html += f'<td rowspan="{total_p}" style="text-align:center;">{priority}</td>'
                                first_priority_cell = False
                            html += f'<td>{row[("", "", "vulnérabilité")]}</td>'
                            # Add remédiation column
                            remediation_value = row.get(("", "", "recommandation"), "")
                            html += f'<td>{remediation_value}</td>'
                            # Add data cells for each client-version combination
                            for client, data in client_groups.items():
                                for version_header, date_header in data['versions'].items():
                                    cell_value = row.get((client, version_header, date_header), "N/A")
                                    if pd.isna(cell_value) or cell_value == "":
                                        cell_value = "N/A"
                                    html += f'<td style="text-align:center;">{cell_value}</td>'
                            html += '</tr>'
        else:
            # Original single-level header logic
            html += '<thead><tr style="text-align: center; background-color: #f2f2f2;">'
            html += '<th>Priorité</th>'
            if show_year:
                html += '<th>Année</th>'
            html += '<th>Vulnérabilité</th>'
            html += '<th>Remédiation</th>'
            client_columns = [col for col in pivot_df.columns if col not in final_pivot_cols]
            for col in client_columns:
                html += f'<th>{col}</th>'
            html += '</tr></thead><tbody>'

            for priority, pgroup in pivot_df.groupby("priorité", sort=False):
                total_p = len(pgroup)
                row_style = priority_style(priority)
                first_priority_cell = True
                if show_year:
                    for year, ygroup in pgroup.groupby("année", sort=False):
                        total_y = len(ygroup)
                        first_year_cell = True
                        for _, row in ygroup.iterrows():
                            html += f'<tr style="{row_style}">'
                            if first_priority_cell:
                                html += f'<td rowspan="{total_p}" style="text-align:center;">{priority}</td>'
                                first_priority_cell = False
                            if first_year_cell:
                                html += f'<td rowspan="{total_y}" style="text-align:center;">{year}</td>'
                                first_year_cell = False
                            html += f'<td>{row["vulnérabilité"]}</td>'
                            # Add remédiation column
                            remediation_value = row.get("recommandation", "")
                            html += f'<td>{remediation_value}</td>'
                            for col in client_columns:
                                status = row[col] if pd.notna(row[col]) and row[col] != "" else "N/A"
                                html += f'<td style="text-align:center;">{status}</td>'
                            html += '</tr>'
                else:
                    for _, row in pgroup.iterrows():
                        html += f'<tr style="{row_style}">'
                        if first_priority_cell:
                            html += f'<td rowspan="{total_p}" style="text-align:center;">{priority}</td>'
                            first_priority_cell = False
                        html += f'<td>{row["vulnérabilité"]}</td>'
                        # Add remédiation column
                        remediation_value = row.get("recommandation", "")
                        html += f'<td>{remediation_value}</td>'
                        for col in client_columns:
                            status = row[col] if pd.notna(row[col]) and row[col] != "" else "N/A"
                            html += f'<td style="text-align:center;">{status}</td>'
                        html += '</tr>'

        html += '</tbody></table>'
        st.markdown(html, unsafe_allow_html=True)

    else:
        # Original non-pivoted table logic for when no client data
        sort_cols = ["priorité"]
        if show_year:
            sort_cols.append("année")
        df.sort_values(by=sort_cols, inplace=True)

        html = '<table style="width:100%; border-collapse: collapse;" border="1">'
        html += '<thead><tr style="text-align: center; background-color: #f2f2f2;">'
        html += '<th>Priorité</th>'
        if show_year:
            html += '<th>Année</th>'
        html += '<th>Vulnérabilité</th><th>Statut</th></tr></thead><tbody>'

        for priority, pgroup in df.groupby("priorité", sort=False):
            total_p = len(pgroup)
            row_style = priority_style(priority)
            first_priority_cell = True
            if show_year:
                for year, ygroup in pgroup.groupby("année", sort=False):
                    total_y = len(ygroup)
                    first_year_cell = True
                    for row in ygroup.to_dict("records"):
                        html += f'<tr style="{row_style}">'
                        if first_priority_cell:
                            html += f'<td rowspan="{total_p}" style="text-align:center;">{priority}</td>'
                            first_priority_cell = False
                        if first_year_cell:
                            html += f'<td rowspan="{total_y}" style="text-align:center;">{year}</td>'
                            first_year_cell = False
                        html += f'<td>{row["vulnérabilité"]}</td>'
                        html += f'<td style="text-align:center;">{row["statut"]}</td>'
                        html += '</tr>'
            else:
                for row in pgroup.to_dict("records"):
                    html += f'<tr style="{row_style}">'
                    if first_priority_cell:
                        html += f'<td rowspan="{total_p}" style="text-align:center;">{priority}</td>'
                        first_priority_cell = False
                    html += f'<td>{row["vulnérabilité"]}</td>'
                    html += f'<td style="text-align:center;">{row["statut"]}</td>'
                    html += '</tr>'

        html += '</tbody></table>'
        st.markdown(html, unsafe_allow_html=True)

def app():
    script_dir = os.path.dirname(os.path.abspath(__file__))
    folder_path = os.path.join(script_dir, "plansremediationsSansOneDrive")

    def find_excel_files(root_folder):
        # Returns list of all Excel files in the folder hierarchy (ignoring 'R&D')
        excel_files = []
        for dirpath, _, filenames in os.walk(root_folder):
            if 'R&D' in dirpath:
                continue
            for file in filenames:
                if file.endswith(".xlsx"):
                    excel_files.append(os.path.join(dirpath, file))
        return excel_files

    column_mappings = {
        "vulnérabilité": ["vulnérabilité", "vulnerability", "faille"],
        "priorité": ["priorité", "priority", "criticité", "criticity"],
        "équipe en charge": ["équipe en charge", "equipe en charge", "équipe encharge",
                              "equipe encharge", "equipe projet", "intervenant", "équipe responsable"],
        "statut": ["statut", "statuts", "status"],
        "fix_type": ["fix type", "fix_type", "type de correction", "type correction"],
        "recommandation": ["recommandation", "recommendation", "remédiation", "remediation"]
    }

    def find_column(df, possible_names):
        norm_possible = [normalize_col_name(name) for name in possible_names]
        for col in df.columns:
            if normalize_col_name(col) in norm_possible:
                return col
        return None

    def load_restitution_date(file_path):
        sheet_names = ["Déroulement", "Schedule"]
        for sheet in sheet_names:
            try:
                df = pd.read_excel(file_path, sheet_name=sheet, skiprows=lambda x: x > 10)
                df = df.dropna(how='all', axis=0).dropna(how='all', axis=1)
                df.columns = df.iloc[0]
                df = df[1:].reset_index(drop=True)
                if 'Restitution' in df.columns:
                    first_date = df['Restitution'].dropna().iloc[0]
                    if isinstance(first_date, (int, float)):
                        first_date = pd.to_datetime("1899-12-30") + pd.to_timedelta(first_date, "D")
                    elif isinstance(first_date, str):
                        try:
                            cleaned_date = re.sub(r'^[A-Za-zéû]+\s+', '', first_date).strip()
                            first_date = pd.to_datetime(cleaned_date, dayfirst=True)
                        except ValueError:
                            return []
                    return [first_date.strftime("%d/%m/%Y")]
            except Exception:
                pass
        return []

    def extract_years_and_months_from_restitution_dates(restitution_dates):
        years_months = []
        for date_str in restitution_dates:
            if isinstance(date_str, str):
                dt = datetime.strptime(date_str, '%d/%m/%Y')
                years_months.append((dt.year, dt.month))
        return sorted(set(years_months))

    def calculate_deadline(row, restitution_dates, priorite_col):
        restitution_date = restitution_dates[0] if restitution_dates else None
        if restitution_date and priorite_col in row:
            restitution_date = datetime.strptime(restitution_date, '%d/%m/%Y')
            priority_value = str(row[priorite_col]).strip().upper()
            if 'P1' in priority_value:
                deadline_date = restitution_date + timedelta(days=30)
            elif 'P2' in priority_value:
                deadline_date = restitution_date + timedelta(days=60)
            elif 'P3' in priority_value:
                deadline_date = restitution_date + timedelta(days=180)
            else:
                deadline_date = None
            if deadline_date:
                return deadline_date.strftime('%d/%m/%Y')
        return None

    def calculate_deadline_compliance(row, statut_col):
        if statut_col in row and 'Deadline' in row:
            status_value = str(row[statut_col]).strip().lower()
            if 'non corrigée' in status_value or 'en cours' in status_value:
                try:
                    deadline_date = datetime.strptime(row['Deadline'], '%d/%m/%Y')
                    if deadline_date > datetime.now():
                        return 'En attente'
                    else:
                        return 'Manqué'
                except Exception:
                    return None
        return None

    def load_data(file_path, restitution_dates):
        try:
            df = pd.read_excel(file_path, sheet_name='Vulnérabilités')
        except ValueError:
            try:
                df = pd.read_excel(file_path, sheet_name='Vulnerabilities')
            except ValueError:
                st.warning(f"Sheet 'Vulnérabilités' not found in {os.path.basename(file_path)}. Skipping file.")
                return pd.DataFrame()  # Return an empty DataFrame so it doesn't affect the merge

        # Clean column names
        df.columns = df.columns.str.strip()

        # Map columns to standard names
        for key, possible_names in column_mappings.items():
            col_found = find_column(df, possible_names)
            if col_found:
                df.rename(columns={col_found: key}, inplace=True)

        df['source_file'] = os.path.basename(file_path)

        # Check if Fix Type column is missing (silent check)
        if 'fix_type' not in df.columns:
            # Only show warning if there are actual missing columns (for troubleshooting)
            pass

        # Get required columns
        required_columns = ["priorité", "équipe en charge"]

        # Drop rows without valid team or priority
        df.dropna(subset=required_columns, inplace=True)

        # Transform statut values
        if "statut" in df.columns:
            df["statut"] = df["statut"].fillna('').astype(str).apply(transform_statut)

        # Add year from restitution date
        if restitution_dates:
            df["année"] = datetime.strptime(restitution_dates[0], '%d/%m/%Y').year

        # Calculate deadline and compliance
        if "priorité" in df.columns:
            df['Deadline'] = df.apply(lambda row: calculate_deadline(row, restitution_dates, "priorité"), axis=1)
            if "statut" in df.columns:
                df['Conformité des délais'] = df.apply(lambda row: calculate_deadline_compliance(row, "statut"), axis=1)
            else:
                df['Conformité des délais'] = None

        return df

    def visualize_data(df, title):
        priorite_col = find_column(df, column_mappings["priorité"])
        statut_col   = find_column(df, column_mappings["statut"])
        equipe_col   = find_column(df, column_mappings["équipe en charge"])
        vuln_col     = find_column(df, column_mappings["vulnérabilité"])

        # Chart 1: Bar & Pie by Priority
        count_by_priorite = (
            df[priorite_col]
            .value_counts()
            .reindex(['P1','P2','P3'], fill_value=0)
            .reset_index()
        )
        count_by_priorite.columns = [priorite_col, 'count']
        count_by_priorite[priorite_col] = pd.Categorical(count_by_priorite[priorite_col], categories=['P1','P2','P3'], ordered=True)
        count_by_priorite = count_by_priorite.sort_values(by=priorite_col)

        col1, col2 = st.columns([1,1])
        custom_colors = {'P1':'#FF0000','P2':'#ED7D31','P3':'#FFC000'}

        with col1:
            fig_bar = px.bar(
                count_by_priorite,
                x=priorite_col,
                y='count',
                title="Nombre de failles par priorité",
                text_auto=True,
                color=priorite_col,
                color_discrete_map=custom_colors,
                height=500
            )
            fig_bar.update_xaxes(title="", linecolor='black', tickfont=dict(color='black'))
            fig_bar.update_yaxes(title="Nombre de failles", linecolor='black', tickfont=dict(color='black'))
            fig_bar.update_layout(legend_title_text='Priorité')
            st.plotly_chart(fig_bar, use_container_width=True)

        with col2:
            fig_pie = px.pie(
                count_by_priorite,
                values='count',
                names=priorite_col,
                color=priorite_col,
                color_discrete_map=custom_colors
            )
            st.plotly_chart(fig_pie, use_container_width=True)

        st.markdown("_ _ _")

        # Standardize 'statut' & 'équipe en charge'
        def standardize_statut(value):
            val = str(value).strip().lower()
            if val in ['clôs', 'c l o s', 'c lôs', 'clos', 'corrigée', 'corrigé', 'corigée']:
                return 'corrigée'
            elif val in ['en cours', 'non clôs', 'non clos', 'non corrigée']:
                return 'non corrigée'
            return val

        if statut_col:
            df[statut_col] = df[statut_col].apply(standardize_statut)

        if equipe_col:
            df[equipe_col] = df[equipe_col].str.strip().str.lower().replace({
                'tma interne': 'TMA', 'séc.op': 'PRODOPS', 'sec.op': 'PRODOPS', 'r&d': 'R&D',
                'prodops': 'PRODOPS', 'tma': 'TMA', 'pôle sécurité': 'PRODOPS',
                'r&d / prodops': 'R&D / PRODOPS', 'r&d et prodops': 'R&D / PRODOPS'
            })

        df['Conformité des délais'] = df.apply(lambda row: calculate_deadline_compliance(row, statut_col), axis=1)

        # Chart 2: Count by Equipe en charge, Priority, and Statut
        # Total count (grouped by équipe and priorité)
        total_counts = df.groupby([equipe_col, priorite_col]).size().reset_index(name='total')

        # Corrigée only
        corrigée_counts = df[df[statut_col] == 'corrigée'].groupby([equipe_col, priorite_col]).size().reset_index(name='corrigée')

        # Merge total and corrigée
        merged_counts = pd.merge(total_counts, corrigée_counts, on=[equipe_col, priorite_col], how='left')
        merged_counts['corrigée'] = merged_counts['corrigée'].fillna(0).astype(int)

        # Melt for grouped bar chart
        count_by_criticite_perimetre = merged_counts.melt(
            id_vars=[equipe_col, priorite_col],
            value_vars=['total', 'corrigée'],
            var_name='statut',
            value_name='count'
        )
        # ------------------------------------------------------------
        # right after you create `count_by_criticite_perimetre`
        df_all = count_by_criticite_perimetre.copy()

        # 1️⃣ keep only real priorities
        df_all = df_all[df_all[priorite_col].isin(['P1', 'P2', 'P3'])]

        # 2️⃣ drop empty bars – they only take space
        df_all = df_all[df_all['count'] > 0]

        # 3️⃣ rebuild the “statut” label (no more total_nan)
        df_all['statut'] = df_all.apply(
                lambda r: f"total_{r[priorite_col]}" if r['statut'] == 'total' else r['statut'],
                axis=1
        )

        # 4️⃣ categorical order
        df_all[priorite_col] = pd.Categorical(df_all[priorite_col],
                                            categories=['P1', 'P2', 'P3'],
                                            ordered=True)
        # ------------------------------------------------------------

        # Add color based on statut and priorité
        def assign_color(row):
            if row['statut'] == 'corrigée':
                return '#2ca02c'  # green
            elif row[priorite_col] == 'P1':
                return '#FF0000'
            elif row[priorite_col] == 'P2':
                return '#ED7D31'
            elif row[priorite_col] == 'P3':
                return '#FFC000'
            return '#cccccc'

        count_by_criticite_perimetre['color'] = count_by_criticite_perimetre.apply(assign_color, axis=1)


        count_by_criticite_perimetre[priorite_col] = pd.Categorical(count_by_criticite_perimetre[priorite_col], categories=['P1','P2','P3'], ordered=True)
        count_by_criticite_perimetre[priorite_col] = count_by_criticite_perimetre[priorite_col].astype(str)

        # Create spacer rows between priorities
        # Ensure correct order and type
        count_by_criticite_perimetre[priorite_col] = pd.Categorical(
            count_by_criticite_perimetre[priorite_col],
            categories=['P1', 'P2', 'P3'],
            ordered=True
        )
        count_by_criticite_perimetre.sort_values(by=priorite_col, inplace=True)


        df_all = count_by_criticite_perimetre.copy()
        df_all[priorite_col] = pd.Categorical(df_all[priorite_col],
                                            categories=['P1', 'P2', 'P3'],
                                            ordered=True)

                
        # turn every “total” into a per-priority category
        # Keep raw labels (only "total" and "corrigée")
        plot_df = df_all[df_all['count'] > 0].copy()

        priority_colors = {'P1': '#FF0000', 'P2': '#ED7D31', 'P3': '#FFC000'}
        fig_criticite_bar = px.bar(
            plot_df,
            x=priorite_col,
            y='count',
            color='statut',        # now only "total" and "corrigée"
            text='count',
            facet_col=equipe_col,
            barmode='group',
            color_discrete_map={'total': '#000000',   # temp colour
                                'corrigée': '#2ca02c'}
        )

        # Re-colour the "total" traces per priority
        for tr in fig_criticite_bar.data:
            if tr.name == 'total':                    # only the total traces
                # Create a list of colors for each bar based on its x-value (priority)
                colors = []
                for x_val in tr.x:
                    colors.append(priority_colors.get(x_val, '#000000'))
                tr.marker.color = colors
                # Hide "total" from legend
                tr.showlegend = False

        fig_criticite_bar.update_layout(
            bargap=0.2,     # 20% gap between P1/P2/P3 clusters
            bargroupgap=0,  # no gap inside each cluster
            legend_title_text='Statut'
        )




        fig_criticite_bar.for_each_annotation(lambda a: a.update(text=a.text.split("=")[-1]))
        fig_criticite_bar.update_xaxes(title="", linecolor='black', tickfont=dict(color='black'))
        fig_criticite_bar.update_yaxes(title="Nombre de failles", linecolor='black', tickfont=dict(color='rgba(0,0,0,0)'),
                                      row=1, col=1)
        fig_criticite_bar.update_yaxes(title="", linecolor='black', tickfont=dict(color='black'), row=1, col=2)
        fig_criticite_bar.update_yaxes(title="", linecolor='black', tickfont=dict(color='black'), row=1, col=3)



        st.plotly_chart(fig_criticite_bar, use_container_width=True, key=f"criticite_chart_{title}_{uuid.uuid4()}")

        st.markdown("_ _ _")

        # Chart 3: Deadline Compliance
        conformity_col = "Conformité des délais"
        compliance_df = df.query(f"`{conformity_col}` == 'Manqué' or `{conformity_col}` == 'En attente'")
        count_by_deadline_compliance = (compliance_df
                                        .groupby([priorite_col, conformity_col])
                                        .size()
                                        .unstack(fill_value=0)
                                        .stack()
                                        .reset_index(name='count'))
        count_by_deadline_compliance[priorite_col] = pd.Categorical(count_by_deadline_compliance[priorite_col],
                                                                    categories=['P1','P2','P3'], ordered=True)
        count_by_deadline_compliance = count_by_deadline_compliance.sort_values(by=priorite_col)

        fig_compliance = px.bar(
            count_by_deadline_compliance,
            x=priorite_col,
            y='count',
            color=conformity_col,
            text_auto=True,
            title="Conformité des délais de correction par priorité",
            color_discrete_map={'Manqué':'#873260','En attente':'#d8bfd8'}
        )
        fig_compliance.update_layout(height=500, width=400, legend_title_text='Conformité des délais')
        fig_compliance.update_xaxes(title="", linecolor='black', tickfont=dict(color='black'))
        fig_compliance.update_yaxes(title="Nombre de failles", linecolor='black', tickfont=dict(color='black'))

        # Create columns for the compliance chart and table
        colA, colB = st.columns(2)

        # Display compliance chart in column A
        with colA:
            st.plotly_chart(fig_compliance, key="compliance_chart", use_container_width=True)

        # Table: non-corrected vulnerabilities in column B
        with colB:
            # Create a header with a toggle button for the non-corrected vulnerabilities table
            col1, col2 = st.columns([3, 1])
            with col1:
                st.markdown("### Table des vulnérabilités non corrigées")
            with col2:
                # Initialize session state for table visibility if it doesn't exist
                if 'show_noncorrected_table' not in st.session_state:
                    st.session_state.show_noncorrected_table = True

                # Create toggle button
                show_table = st.toggle("Afficher", value=st.session_state.show_noncorrected_table, key="toggle_noncorrected_table")

                # Update session state when toggle changes
                if show_table != st.session_state.show_noncorrected_table:
                    st.session_state.show_noncorrected_table = show_table

            # Only show the table if the toggle is on
            if st.session_state.show_noncorrected_table and statut_col and vuln_col and priorite_col and equipe_col:
                filtered_data = (
                    df[df[statut_col] == 'non corrigée'][[vuln_col, priorite_col, conformity_col, equipe_col]]
                    .copy()
                )
                filtered_data[priorite_col] = pd.Categorical(filtered_data[priorite_col], categories=["P1", "P2", "P3"], ordered=True)
                filtered_data.sort_values(by=priorite_col, inplace=True)


                # Check if there's data to display
                if filtered_data.empty:
                    st.info("Aucune vulnérabilité non corrigée à afficher.")
                else:
                    def apply_priority_colors(sub_df):
                        def priority_color(row):
                            if row[priorite_col] == 'P1':
                                return ['background-color: #FF0000; color: white'] * len(row)
                            elif row[priorite_col] == 'P2':
                                return ['background-color: #ED7D31; color: black'] * len(row)
                            elif row[priorite_col] == 'P3':
                                return ['background-color: #FFC000; color: black'] * len(row)
                            else:
                                return [''] * len(row)
                        return sub_df.style.apply(priority_color, axis=1)

                    styled_table = apply_priority_colors(filtered_data)
                    styled_table = styled_table.hide(axis='index').set_table_styles(
                        [{'selector': 'th', 'props': [('text-align', 'center')]}]
                    )
                    table_html = styled_table.to_html()
                    st.write(table_html, unsafe_allow_html=True)
            elif not st.session_state.show_noncorrected_table:
                st.info("La table des vulnérabilités non corrigées est masquée. Utilisez le bouton 'Afficher' pour l'afficher.")

    # Helper: use folder structure to get client name (top-level folder)
    def get_client_name(file_path):
        relative_path = os.path.relpath(file_path, folder_path)
        parts = relative_path.split(os.sep)
        if parts:
            return parts[0]
        return os.path.basename(os.path.dirname(file_path))

    def extract_short_name(file_path):
        base_name = os.path.basename(file_path).replace('.xlsx', '').strip()
        parts = [p.strip() for p in base_name.split('_') if p.strip()]
        ignore_tokens = {'plan', 'remdédiation', 'remédiation', 'reméidation', 'remediation', 'de', 'du', 'la', 'des'}
        for part in parts:
            tokens = [t.strip() for t in part.split('-') if t.strip()]
            for token in tokens:
                if token.lower() not in ignore_tokens:
                    return token
        return base_name
    def extract_version_from_filename(file_path):
        filename = os.path.basename(file_path).replace('.xlsx', '').strip()
        
        # Get just the filename without path
        base_filename = os.path.basename(file_path)
        
        # Return the full filename for more accurate version identification
        # This ensures unique identification even for complex filenames
        return base_filename.replace('.xlsx', '')
    def extract_title_from_filename(file_path):
        base_name = os.path.basename(file_path).replace('.xlsx','')
        if 'Plan-Remediation_' in base_name:
            name_part = base_name.split('Plan-Remediation_')[1]
            short_name = name_part.split('_')[0]
            suffix = name_part.split('_',1)[1] if '_' in name_part else ''
            return f"{short_name}: {suffix}"
        return base_name

    def main():
        # Let user choose the mode of selection
        mode = "Par date"
        all_full_paths = find_excel_files(folder_path)
        if not all_full_paths:
            st.warning("Aucun fichier Excel trouvé (ou dossier R&D ignoré).")
            return

        # Store filter selections in session state if not already there
        if 'filter_mode' not in st.session_state:
            st.session_state.filter_mode = mode
        if 'filter_year' not in st.session_state:
            st.session_state.filter_year = None
        if 'filter_month' not in st.session_state:
            st.session_state.filter_month = None
        if 'filter_client' not in st.session_state:
            st.session_state.filter_client = None
        if 'filtered_files' not in st.session_state:
            st.session_state.filtered_files = []
        if 'client_to_files' not in st.session_state:
            st.session_state.client_to_files = {}

        # Update mode if changed
        if mode != st.session_state.filter_mode:
            st.session_state.filter_mode = mode
            st.session_state.filter_year = None
            st.session_state.filter_month = None
            st.session_state.filter_client = None
            st.session_state.filtered_files = []
            st.session_state.client_to_files = {}

        if mode == "Par date":
            # Collect restitution dates from all files
            all_restitution_dates = []
            for file_path in all_full_paths:
                restitution_dates = load_restitution_date(file_path)
                all_restitution_dates.extend(restitution_dates)
            available_years_months = extract_years_and_months_from_restitution_dates(all_restitution_dates)
            available_years = sorted(set(year for year, _ in available_years_months))
            if not available_years:
                st.warning("Aucune année disponible.")
                return
            # Sort years descending to put the most recent first
            sorted_years = sorted(available_years, reverse=True)
            sorted_years = sorted(available_years, reverse=True)
            year_options = ["Tous les années"] + [str(y) for y in sorted_years]
            default_year_index = 1  # most recent year

            default_year_index = 1  # Index 1 corresponds to the newest year


            col1, col2 = st.columns(2)
            with col1:
                if st.session_state.filter_year is None or st.session_state.filter_year not in available_years:
                    default_year_index = 1  # Default to first year (index 1 because "Tous les années" is at index 0)
                elif st.session_state.filter_year == "Tous les années":
                    default_year_index = 0
                else:
                    default_year_index = year_options.index(str(st.session_state.filter_year))

                selected_year_str = st.selectbox(
                    "Choisir l'année",
                    year_options,
                    index=default_year_index,
                    key="global_year_selection"
                )

            # Update session state
            if selected_year_str != st.session_state.filter_year:
                st.session_state.filter_year = selected_year_str
                st.session_state.filter_month = None  # Reset month when year changes

            # Get available months based on selected year
            if selected_year_str == "Tous les années":
                available_months = sorted(set(month for _, month in available_years_months))
            else:
                selected_year = int(selected_year_str)
                available_months = sorted(set(month for year, month in available_years_months if year == selected_year))

            if not available_months:
                st.warning(f"Aucun mois disponible pour {selected_year_str}.")
                return

            # Add "Tous les mois" option
            month_options = ["Tous les mois"] + [str(m) for m in available_months]

            # Determine default month selection
            if st.session_state.filter_month is None:
                default_month_index = 0  # Default to "Tous les mois"
            elif st.session_state.filter_month == "Tous les mois":
                default_month_index = 0
            elif str(st.session_state.filter_month) in [str(m) for m in available_months]:
                default_month_index = month_options.index(str(st.session_state.filter_month))
            else:
                default_month_index = 0

            # Create month filter
            with col2:
                selected_month_str = st.selectbox(
                    "Choisir le mois",
                    month_options,
                    index=default_month_index,
                    key="global_month_selection"
                )

            # Update session state
            if selected_month_str != st.session_state.filter_month:
                st.session_state.filter_month = selected_month_str

            # Filter files based on selected year and month
            filtered_files = []
            for path_ in all_full_paths:
                restitution_dates = load_restitution_date(path_)
                for date in restitution_dates:
                    date_obj = datetime.strptime(date, '%d/%m/%Y')

                    # Check if the file matches the selected year and month
                    year_match = (selected_year_str == "Tous les années" or date_obj.year == int(selected_year_str))
                    month_match = (selected_month_str == "Tous les mois" or date_obj.month == int(selected_month_str))

                    if year_match and month_match:
                        filtered_files.append(path_)
                        break

            # Update session state
            st.session_state.filtered_files = filtered_files

            # Group files by client
            client_to_files = {}
            for f in filtered_files:
                client = extract_short_name(f)
                client_to_files.setdefault(client, []).append(f)

            # Update session state
            st.session_state.client_to_files = client_to_files
            # Build version mapping per client
            client_to_versions = {}
            for client, files in client_to_files.items():
                versions = set()
                for f in files:
                    version = extract_version_from_filename(f)
                    if version:
                        versions.add(version)
                client_to_versions[client] = sorted(versions)

            st.session_state.client_to_versions = client_to_versions

            # Create client dropdown
            client_options = ["Tous les clients"] + [client.upper() for client in sorted(client_to_files.keys())]


            # Create client and équipe en charge filters
            col3, col4 = st.columns(2)

            with col3:
                selected_client = st.selectbox(
                    "Choisir le client",
                    client_options,
                    index=0 if st.session_state.filter_client is None else
                        (client_options.index(st.session_state.filter_client) if st.session_state.filter_client in client_options else 0),
                    key="global_client_selection"
                )
                if selected_client != st.session_state.filter_client:
                    st.session_state.filter_client = selected_client
                    st.rerun()



            with col4:
                if selected_client != "Tous les clients" and selected_client in st.session_state.client_to_versions:
                    versions = st.session_state.client_to_versions[selected_client]
                    if len(versions) == 1:
                        version_options = versions
                        default_version_index = 0
                    else:
                        version_options = ["Toutes les versions"] + versions
                        default_version_index = 0
                else:
                    version_options = ["Toutes les versions"]
                    default_version_index = 0

                selected_version = st.selectbox(
                    "Choisir la version",
                    version_options,
                    index=default_version_index,
                    key="global_version_selection"
                )

                st.session_state.filter_version = selected_version







            # Process data based on filters
            if filtered_files:
                if selected_client == "Tous les clients":
                    dfs = []
                    for client, files in client_to_files.items():
                        client_dfs = []
                        for fpath in files:
                            restitution_dates = load_restitution_date(fpath)
                            df = load_data(fpath, restitution_dates)
                            if not df.empty:
                                df["client"] = "BioGroup" if client == "BioGroupe" else client
                                # Add version information extracted from filename
                                version = extract_version_from_filename(fpath)
                                df["version"] = version if version else "No Version"
                                # Add restitution date information
                                restitution_date = restitution_dates[0] if restitution_dates else "No Date"
                                df["restitution_date"] = restitution_date
                                client_dfs.append(df)

                        if client_dfs:
                            client_df = pd.concat(client_dfs, ignore_index=True).drop_duplicates()
                            dfs.append(client_df)
                    if dfs:
                        df_merged = pd.concat(dfs, ignore_index=True)
                        # Create title based on selected filters
                        if selected_year_str == "Tous les années":
                            year_display = "Toutes les années"
                        else:
                            year_display = selected_year_str

                        if selected_month_str == "Tous les mois":
                            month_display = "Tous les mois"
                        else:
                            month_display = selected_month_str

                        visualize_data(df_merged, f"Tous les clients pour {year_display}-{month_display}")
                else:
                    if selected_client in client_to_files:
                        dfs = []
                        for fpath in client_to_files[selected_client]:
                            # Extract version from filename
                            file_version = extract_version_from_filename(fpath)
                            
                            # Skip files that don't match the selected version (if a specific version is selected)
                            if selected_version != "Toutes les versions" and file_version != selected_version:
                                continue
                                
                            restitution_dates = load_restitution_date(fpath)
                            df = load_data(fpath, restitution_dates)
                            if not df.empty:
                                df["client"] = selected_client
                                # Add version information extracted from filename
                                df["version"] = file_version if file_version else "No Version"
                                # Add restitution date information
                                restitution_date = restitution_dates[0] if restitution_dates else "No Date"
                                df["restitution_date"] = restitution_date
                                dfs.append(df)
                        if dfs:
                            df_merged = pd.concat(dfs, ignore_index=True).drop_duplicates()
                            # Create title based on selected filters
                            if selected_year_str == "Tous les années":
                                year_display = "Toutes les années"
                            else:
                                year_display = selected_year_str

                            if selected_month_str == "Tous les mois":
                                month_display = "Tous les mois"
                            else:
                                month_display = selected_month_str

                            visualize_data(df_merged, f"{selected_client} pour {year_display}-{month_display}")

                            # Add download button for the selected client
                            if len(client_to_files[selected_client]) == 1:
                                chosen_path = client_to_files[selected_client][0]
                                with open(chosen_path, "rb") as f:
                                    file_bytes = f.read()
                                st.sidebar.download_button(
                                    label="Télécharger le fichier Excel",
                                    data=file_bytes,
                                    file_name=os.path.basename(chosen_path),
                                    mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                                )
            else:
                # Create warning message based on selected filters
                if selected_year_str == "Tous les années":
                    year_display = "toutes les années"
                else:
                    year_display = selected_year_str

                if selected_month_str == "Tous les mois":
                    month_display = "tous les mois"
                else:
                    month_display = selected_month_str

                st.warning(f"Aucun fichier trouvé pour l'année {year_display} et le mois {month_display}.")


    # Run the main function for Plans de Remédiation
    main()

    # Add a separator before the table section
    st.markdown("---")

    # Create a header with a toggle button for the vulnerability table
    col1, col2 = st.columns([3, 1])
    with col1:
        st.markdown("## Tableau des vulnérabilités")
    with col2:
        # Initialize session state for table visibility if it doesn't exist
        if 'show_vulnerability_table' not in st.session_state:
            st.session_state.show_vulnerability_table = True

        # Create toggle button
        show_table = st.toggle("Afficher le tableau", value=st.session_state.show_vulnerability_table, key="toggle_vulnerability_table")

        # Update session state when toggle changes
        if show_table != st.session_state.show_vulnerability_table:
            st.session_state.show_vulnerability_table = show_table

    # Use the global filters from session state
    if 'filter_mode' in st.session_state and 'filtered_files' in st.session_state and 'client_to_files' in st.session_state:
        mode = st.session_state.filter_mode
        filtered_files = st.session_state.filtered_files
        client_to_files = st.session_state.client_to_files
        selected_client = st.session_state.filter_client

        # Process data based on filters
        df_merged = None

        if mode == "Par date" and filtered_files:
            if selected_client == "Tous les clients":
                # Combine data from all clients
                dfs = []
                for client, files in client_to_files.items():
                    client_dfs = []
                    for f in files:
                        dates = load_restitution_date(f)
                        df = load_data(f, dates)
                        if not df.empty:
                            df["client"] = client
                            # Add version information extracted from filename
                            version = extract_version_from_filename(f)
                            df["version"] = version if version else "No Version"
                            # Add restitution date information
                            restitution_date = dates[0] if dates else "No Date"
                            df["restitution_date"] = restitution_date
                            client_dfs.append(df)
                    if client_dfs:
                        client_df = pd.concat(client_dfs, ignore_index=True).drop_duplicates()
                        dfs.append(client_df)
                if dfs:
                    df_merged = pd.concat(dfs, ignore_index=True)
            elif selected_client in client_to_files:
                # Process selected client
                dfs = []
                for f in client_to_files[selected_client]:
                    dates = load_restitution_date(f)
                    df = load_data(f, dates)
                    if not df.empty:
                        df["client"] = selected_client
                        # Add version information extracted from filename
                        version = extract_version_from_filename(f)
                        df["version"] = version if version else "No Version"
                        # Add restitution date information
                        restitution_date = dates[0] if dates else "No Date"
                        df["restitution_date"] = restitution_date
                        dfs.append(df)
                if dfs:
                    df_merged = pd.concat(dfs, ignore_index=True).drop_duplicates()

        elif mode == "Par client" and client_to_files:
            if selected_client == "Tous les clients":
                # Combine data from all clients
                all_dfs = []
                for client, client_files in client_to_files.items():
                    client_dfs = []
                    for fpath in client_files:
                        restitution_dates = load_restitution_date(fpath)
                        df = load_data(fpath, restitution_dates)
                        if not df.empty:
                            df["client"] = client
                            # Add version information extracted from filename
                            version = extract_version_from_filename(fpath)
                            df["version"] = version if version else "No Version"
                            # Add restitution date information
                            restitution_date = restitution_dates[0] if restitution_dates else "No Date"
                            df["restitution_date"] = restitution_date
                            client_dfs.append(df)
                    if client_dfs:
                        client_df = pd.concat(client_dfs, ignore_index=True).drop_duplicates()
                        all_dfs.append(client_df)
                if all_dfs:
                    df_merged = pd.concat(all_dfs, ignore_index=True)
            elif selected_client in client_to_files:
                # Process selected client
                client_files = client_to_files[selected_client]
                dfs = []
                for fpath in client_files:
                    restitution_dates = load_restitution_date(fpath)
                    df = load_data(fpath, restitution_dates)
                    if not df.empty:
                        df["client"] = selected_client
                        # Add version information extracted from filename
                        version = extract_version_from_filename(fpath)
                        df["version"] = version if version else "No Version"
                        # Add restitution date information
                        restitution_date = restitution_dates[0] if restitution_dates else "No Date"
                        df["restitution_date"] = restitution_date
                        dfs.append(df)
                if dfs:
                    df_merged = pd.concat(dfs, ignore_index=True).drop_duplicates()

        # Display the table if data is available
        if df_merged is not None:
            # Filter out empty statut rows
            df_merged = df_merged[df_merged["statut"].astype(str).str.strip() != ""]




            # Determine if we should show year
            show_year = True
            if mode == "Par date" and 'filter_year' in st.session_state and st.session_state.filter_year is not None:
                show_year = False

            # Display the table only if the toggle is on
            if st.session_state.show_vulnerability_table:
                display_table_only(df_merged, show_year=show_year)
            else:
                st.info("Le tableau est actuellement masqué. Utilisez le bouton 'Afficher le tableau' pour l'afficher.")

            # Excel download section
            show_client = "client" in df_merged.columns

            if show_client:
                # Check if version column exists for two-level pivot in download
                has_version_download = "version" in df_merged.columns

                # STEP 0: NORMALIZE VULNERABILITY NAMES for download data
                df_download_normalized = df_merged.copy()

                def normalize_vulnerability(vuln):
                    if pd.isna(vuln):
                        return vuln
                    normalized = str(vuln).lower().strip()
                    normalized = normalized.replace('  ', ' ')  # Replace double spaces with single space
                    return normalized

                df_download_normalized['vulnérabilité_normalized'] = df_download_normalized['vulnérabilité'].apply(normalize_vulnerability)

                # Create pivoted download data
                pivot_cols = ["priorité", "vulnérabilité_normalized"]
                if show_year:
                    pivot_cols.insert(1, "année")

                if has_version_download:
                    # STEP 1: Create three-level column index pivot table for download
                    # First, create a combined client-restitution-version identifier for pivot
                    df_download_normalized['client_restitution_version'] = (df_download_normalized['client'].astype(str) + '|' +
                                                                          df_download_normalized['restitution_date'].fillna('No Date').astype(str) + '|' +
                                                                          df_download_normalized['version'].fillna('').astype(str))

                    # Create pivot table with client-restitution-version combinations
                    download_df = df_download_normalized.pivot_table(
                        index=pivot_cols,
                        columns="client_restitution_version",
                        values="statut",
                        aggfunc=lambda x: ' / '.join(x.dropna().unique()) if len(x.dropna().unique()) > 1 else x.iloc[0] if len(x.dropna()) > 0 else "N/A",
                        fill_value="N/A"
                    ).reset_index()

                    # STEP 2: Convert to three-level MultiIndex columns for download
                    multi_index_tuples = []
                    for col in download_df.columns:
                        if col in pivot_cols:
                            multi_index_tuples.append(('', '', col))  # Empty first two levels for index columns
                        elif '|' in str(col):
                            parts = str(col).split('|')
                            if len(parts) >= 3:
                                client, restitution_date, version = parts[0], parts[1], parts[2]
                                version_header = f"Version: {version}" if version else "Version: No Version"
                                date_header = f"Date restitution: {restitution_date}" if restitution_date != "No Date" else "Date restitution: No Date"
                                multi_index_tuples.append((client, version_header, date_header))
                            else:
                                multi_index_tuples.append(('', '', col))
                        else:
                            multi_index_tuples.append(('', '', col))

                    # Create MultiIndex columns
                    download_df.columns = pd.MultiIndex.from_tuples(multi_index_tuples, names=['Client', 'Version', 'Date'])

                    # STEP 3: Map back to original vulnerability names for display
                    vuln_mapping = df_download_normalized.groupby('vulnérabilité_normalized')['vulnérabilité'].first().to_dict()
                    download_df[('', '', 'vulnérabilité')] = download_df[('', '', 'vulnérabilité_normalized')].map(vuln_mapping)
                    download_df = download_df.drop(('', '', 'vulnérabilité_normalized'), axis=1)

                    # STEP 4: THEN DEDUPLICATE - Remove any remaining duplicate rows
                    final_pivot_cols = [('', '', "priorité"), ('', '', "vulnérabilité")]
                    if show_year:
                        final_pivot_cols.insert(1, ('', '', "année"))
                    download_df = download_df.drop_duplicates(subset=final_pivot_cols).reset_index(drop=True)
                    download_df[('', '', "priorité")] = pd.Categorical(download_df[('', '', "priorité")], categories=["P1", "P2", "P3"], ordered=True)
                    download_df = download_df.sort_values(by=('', '', "priorité"))
                    download_df.reset_index(drop=True, inplace=True)
                else:
                    # STEP 1: MERGE FIRST - Create pivot table that merges all client data (original logic)
                    download_df = df_download_normalized.pivot_table(
                        index=pivot_cols,
                        columns="client",
                        values="statut",
                        aggfunc=lambda x: ' / '.join(x.dropna().unique()) if len(x.dropna().unique()) > 1 else x.iloc[0] if len(x.dropna()) > 0 else "N/A",  # Merge multiple statuses
                        fill_value="N/A"
                    ).reset_index()

                    # STEP 2: Map back to original vulnerability names for display
                    vuln_mapping = df_download_normalized.groupby('vulnérabilité_normalized')['vulnérabilité'].first().to_dict()
                    download_df['vulnérabilité'] = download_df['vulnérabilité_normalized'].map(vuln_mapping)

                    # Drop normalized column and reorder
                    download_df = download_df.drop('vulnérabilité_normalized', axis=1)
                    final_pivot_cols = ["priorité", "vulnérabilité"]
                    if show_year:
                        final_pivot_cols.insert(1, "année")

                    client_columns = [col for col in download_df.columns if col not in final_pivot_cols]
                    download_df = download_df[final_pivot_cols + client_columns]

                    # STEP 3: THEN DEDUPLICATE - Remove any remaining duplicate rows
                    download_df = download_df.drop_duplicates(subset=final_pivot_cols).reset_index(drop=True)

                    download_df["priorité"] = pd.Categorical(download_df["priorité"], categories=["P1", "P2", "P3"], ordered=True)
                    download_df.sort_values(by="priorité", inplace=True)
                    download_df.reset_index(drop=True, inplace=True)
            else:
                # Original non-pivoted download logic
                cols_to_download = ["priorité", "vulnérabilité", "statut"]
                if show_year:
                    cols_to_download.insert(1, "année")
                download_df = df_merged[cols_to_download].drop_duplicates().copy()

                sort_cols = ["priorité"]
                if show_year:
                    sort_cols.append("année")
                download_df["priorité"] = pd.Categorical(download_df["priorité"], categories=["P1", "P2", "P3"], ordered=True)
                download_df.sort_values(by=sort_cols, inplace=True)
                download_df.reset_index(drop=True, inplace=True)

            # Create Excel file in memory
            output = io.BytesIO()
            with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
                # Handle MultiIndex columns for Excel export
                if show_client and has_version_download:
                    # For MultiIndex columns, we need to flatten them for Excel
                    flattened_df = download_df.copy()
                    # Flatten MultiIndex columns to single level (three levels to one)
                    flattened_df.columns = [f"{col[0]} - {col[1]}" if col[0] != '' else col[2] for col in download_df.columns]
                    flattened_df.to_excel(writer, index=False, sheet_name='Table')
                else:
                    download_df.to_excel(writer, index=False, sheet_name='Table')

                workbook = writer.book
                worksheet = writer.sheets['Table']

                header_format = workbook.add_format({'bold': True, 'bg_color': '#f2f2f2', 'align': 'center', 'valign': 'vcenter'})

                if show_client and has_version_download:
                    # Write flattened headers
                    for col_num, value in enumerate(flattened_df.columns.values):
                        worksheet.write(0, col_num, value, header_format)
                else:
                    for col_num, value in enumerate(download_df.columns.values):
                        worksheet.write(0, col_num, value, header_format)

                if show_client and has_version_download:
                    # Set column widths for flattened columns
                    for idx, col in enumerate(flattened_df.columns):
                        column_len = max(flattened_df[col].astype(str).map(len).max(), len(col)) + 2
                        worksheet.set_column(idx, idx, column_len)
                else:
                    for idx, col in enumerate(download_df.columns):
                        column_len = max(download_df[col].astype(str).map(len).max(), len(col)) + 2
                        worksheet.set_column(idx, idx, column_len)

                # Predefine formats based on priorité, with vertical centering
                format_p1 = workbook.add_format({'bg_color': '#FF0000', 'font_color': 'white', 'align': 'center', 'valign': 'vcenter'})
                format_p2 = workbook.add_format({'bg_color': '#ED7D31', 'font_color': 'black', 'align': 'center', 'valign': 'vcenter'})
                format_p3 = workbook.add_format({'bg_color': '#FFC000', 'font_color': 'black', 'align': 'center', 'valign': 'vcenter'})
                format_default = workbook.add_format({'align': 'center', 'valign': 'vcenter'})

                if show_client:
                    if has_version_download:
                        # Handle MultiIndex pivoted table formatting
                        working_df = flattened_df
                        priority_col = "priorité"
                        year_col = "année" if show_year else None

                        merge_columns = set([priority_col])
                        if year_col:
                            merge_columns.add(year_col)

                        for col_idx, col in enumerate(working_df.columns):
                            if col_idx in merge_columns:
                                worksheet.set_column(col_idx, col_idx, 15)
                            else:
                                worksheet.set_column(col_idx, col_idx, 20)
                            if col_idx in merge_columns:
                                worksheet.write(1, col_idx, col[0], header_format)
                                worksheet.write(2, col_idx, col[1], header_format)
                                worksheet.write(3, col_idx, col[2], header_format)
                            else:
                                worksheet.write(3, col_idx, col, header_format)
                    else:
                        for col_idx, col in enumerate(download_df.columns):
                            worksheet.set_column(col_idx, col_idx, 20)
                            worksheet.write(0, col_idx, col, header_format)

                # Simplified Excel formatting - just write the data without complex formatting
                for row_idx, row in download_df.iterrows():
                    for col_idx, value in enumerate(row):
                        worksheet.write(row_idx + 1, col_idx, value, format_default)
            excel_data = output.getvalue()

            # Add download button
            st.download_button(
                label="Télécharger le tableau en Excel",
                data=excel_data,
                file_name="PlansRemédiation.xlsx",
                mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            )
        else:
            st.warning("Aucune donnée disponible pour les filtres sélectionnés.")



    hide_css = """
    <style>
    #mainMenu {visibility:hidden;}
    footer {visibility:hidden;}
    header {visibility:hidden;}
    </style>
    """
    st.markdown(hide_css, unsafe_allow_html=True)




















